# Modern Portfolio Website

A modern, responsive portfolio website built with Next.js 14, TypeScript, Tailwind CSS, and Framer Motion. Features dark/light mode, smooth animations, and SEO optimization.

## 🚀 Features

- **Modern Tech Stack**: Next.js 14, TypeScript, Tailwind CSS, Framer Motion
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Dark/Light Mode**: Theme switching with persistent user preference
- **Smooth Animations**: Subtle animations and transitions using Framer Motion
- **SEO Optimized**: Meta tags, structured data, sitemap, and robots.txt
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels and alt texts
- **Performance**: Optimized images, lazy loading, and code splitting
- **Type Safe**: Full TypeScript support for better development experience

## 📋 Sections

- **Hero Section**: Professional introduction with call-to-action
- **About Section**: Background, skills overview, and statistics
- **Projects Showcase**: Featured projects with descriptions and links
- **Skills Section**: Technical skills with visual progress bars
- **Contact Section**: Contact form and social media links

## 🛠 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd portfolio-website
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎨 Customization

### Personal Information

Update the following files with your information:

1. **src/app/layout.tsx** - Update metadata (title, description, social links)
2. **src/components/sections/hero.tsx** - Update name, title, bio, and social links
3. **src/components/sections/about.tsx** - Update about content and skills
4. **src/components/sections/projects.tsx** - Add your projects
5. **src/components/sections/contact.tsx** - Update contact information
6. **src/components/structured-data.tsx** - Update structured data for SEO

### Images

Replace placeholder images in the `public` folder:

- Profile photo for hero section
- About section image
- Project screenshots

### Colors and Styling

Customize the color scheme in `src/app/globals.css`:

- Update CSS variables for light and dark themes
- Modify primary, secondary, and accent colors

### Content

Update the following data structures:

- Projects array in `src/components/sections/projects.tsx`
- Skills data in `src/components/sections/skills.tsx` and `src/components/sections/about.tsx`
- Social links in hero and contact sections

## 📦 Build and Deploy

### Build for Production

```bash
npm run build
```

### Deploy

The website can be deployed to various platforms:

- **Vercel** (Recommended): Connect your GitHub repository
- **Netlify**: Deploy from Git or drag & drop build folder
- **GitHub Pages**: Use GitHub Actions for deployment

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Tailwind CSS for the utility-first CSS framework
- Framer Motion for smooth animations
- Lucide React for beautiful icons

---

Built with ❤️ using modern web technologies.

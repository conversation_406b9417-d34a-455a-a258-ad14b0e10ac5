{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\nimport { type ThemeProviderProps } from \"next-themes/dist/types\";\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,iKAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, \"\")\n    .replace(/[\\s_-]+/g, \"-\")\n    .replace(/^-+|-+$/g, \"\");\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,mNAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { Moon, Sun } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\nimport { Button } from \"@/components/ui/button\";\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = React.useState(false);\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return (\n      <Button variant=\"ghost\" size=\"icon\" className=\"w-9 h-9\">\n        <Sun className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Toggle theme</span>\n      </Button>\n    );\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n      className=\"w-9 h-9\"\n    >\n      {theme === \"light\" ? (\n        <Moon className=\"h-4 w-4\" />\n      ) : (\n        <Sun className=\"h-4 w-4\" />\n      )}\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,4JAAQ;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,iNAAc,CAAC;IAE7C,kNAAe,CAAC;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,4IAAM;YAAC,SAAQ;YAAQ,MAAK;YAAO,WAAU;;8BAC5C,8OAAC,uMAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC,4IAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,0MAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,uMAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Menu, X } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { ThemeToggle } from \"@/components/ui/theme-toggle\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"#home\" },\n  { name: \"About\", href: \"#about\" },\n  { name: \"Projects\", href: \"#projects\" },\n  { name: \"Skills\", href: \"#skills\" },\n  { name: \"Contact\", href: \"#contact\" },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [activeSection, setActiveSection] = React.useState(\"home\");\n\n  React.useEffect(() => {\n    const handleScroll = () => {\n      const sections = navItems.map(item => item.href.substring(1));\n      const scrollPosition = window.scrollY + 100;\n\n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const offsetTop = element.offsetTop;\n          const offsetHeight = element.offsetHeight;\n\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const handleNavClick = (href: string) => {\n    setIsOpen(false);\n    const element = document.getElementById(href.substring(1));\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between\">\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <span className=\"text-xl font-bold\">JD</span>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-6\">\n          {navItems.map((item) => (\n            <button\n              key={item.name}\n              onClick={() => handleNavClick(item.href)}\n              className={cn(\n                \"text-sm font-medium transition-colors hover:text-primary relative\",\n                activeSection === item.href.substring(1)\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              )}\n            >\n              {item.name}\n              {activeSection === item.href.substring(1) && (\n                <motion.div\n                  className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-primary\"\n                  layoutId=\"activeSection\"\n                  initial={false}\n                  transition={{ type: \"spring\", stiffness: 380, damping: 30 }}\n                />\n              )}\n            </button>\n          ))}\n        </nav>\n\n        <div className=\"flex items-center space-x-2\">\n          <ThemeToggle />\n          \n          {/* Mobile menu button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setIsOpen(!isOpen)}\n          >\n            {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n            <span className=\"sr-only\">Toggle menu</span>\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"md:hidden border-t bg-background\"\n          >\n            <nav className=\"container py-4 space-y-2\">\n              {navItems.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => handleNavClick(item.href)}\n                  className={cn(\n                    \"block w-full text-left px-4 py-2 text-sm font-medium transition-colors hover:text-primary rounded-md hover:bg-accent\",\n                    activeSection === item.href.substring(1)\n                      ? \"text-primary bg-accent\"\n                      : \"text-muted-foreground\"\n                  )}\n                >\n                  {item.name}\n                </button>\n              ))}\n            </nav>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAU,MAAM;IAAU;IAClC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,iNAAc,CAAC;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,iNAAc,CAAC;IAEzD,kNAAe,CAAC;QACd,MAAM,eAAe;YACnB,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;YAC1D,MAAM,iBAAiB,OAAO,OAAO,GAAG;YAExC,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,YAAY,QAAQ,SAAS;oBACnC,MAAM,eAAe,QAAQ,YAAY;oBAEzC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;wBAC5E,iBAAiB;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,UAAU;QACV,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;QACvD,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uKAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;gCAEC,SAAS,IAAM,eAAe,KAAK,IAAI;gCACvC,WAAW,IAAA,yHAAE,EACX,qEACA,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,iBACA;;oCAGL,KAAK,IAAI;oCACT,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,oBACrC,8OAAC,oMAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAS;wCACT,SAAS;wCACT,YAAY;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;;;;;;;+BAfzD,KAAK,IAAI;;;;;;;;;;kCAsBpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0JAAW;;;;;0CAGZ,8OAAC,4IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU,CAAC;;oCAEzB,uBAAS,8OAAC,iMAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,0MAAI;wCAAC,WAAU;;;;;;kDACtD,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC,4MAAe;0BACb,wBACC,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;gCAEC,SAAS,IAAM,eAAe,KAAK,IAAI;gCACvC,WAAW,IAAA,yHAAE,EACX,wHACA,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,2BACA;0CAGL,KAAK,IAAI;+BATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBhC", "debugId": null}}]}
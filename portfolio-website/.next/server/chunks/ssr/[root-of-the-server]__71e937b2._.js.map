{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Hero = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON>() from the server but <PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/hero.tsx <module evaluation>\",\n    \"Hero\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,OAAO,IAAA,wQAAuB,EACvC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Hero = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON>() from the server but <PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/hero.tsx\",\n    \"Hero\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,OAAO,IAAA,wQAAuB,EACvC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/about.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const About = registerClientReference(\n    function() { throw new Error(\"Attempted to call About() from the server but About is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about.tsx <module evaluation>\",\n    \"About\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,QAAQ,IAAA,wQAAuB,EACxC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/about.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const About = registerClientReference(\n    function() { throw new Error(\"Attempted to call About() from the server but About is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about.tsx\",\n    \"About\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,QAAQ,IAAA,wQAAuB,EACxC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/projects.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Projects = registerClientReference(\n    function() { throw new Error(\"Attempted to call Projects() from the server but Projects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/projects.tsx <module evaluation>\",\n    \"Projects\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,WAAW,IAAA,wQAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/projects.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Projects = registerClientReference(\n    function() { throw new Error(\"Attempted to call Projects() from the server but Projects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/projects.tsx\",\n    \"Projects\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,WAAW,IAAA,wQAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/skills.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Skills = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skills() from the server but Skills is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/skills.tsx <module evaluation>\",\n    \"Skills\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,wQAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/skills.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Skills = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skills() from the server but Skills is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/skills.tsx\",\n    \"Skills\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,wQAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/contact.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Contact = registerClientReference(\n    function() { throw new Error(\"Attempted to call Contact() from the server but Contact is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/contact.tsx <module evaluation>\",\n    \"Contact\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/contact.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Contact = registerClientReference(\n    function() { throw new Error(\"Attempted to call Contact() from the server but Contact is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/contact.tsx\",\n    \"Contact\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/app/page.tsx"], "sourcesContent": ["import { Hero } from \"@/components/sections/hero\";\nimport { About } from \"@/components/sections/about\";\nimport { Projects } from \"@/components/sections/projects\";\nimport { Skills } from \"@/components/sections/skills\";\nimport { Contact } from \"@/components/sections/contact\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Hero />\n      <About />\n      <Projects />\n      <Skills />\n      <Contact />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8IAAI;;;;;0BACL,8OAAC,gJAAK;;;;;0BACN,8OAAC,sJAAQ;;;;;0BACT,8OAAC,kJAAM;;;;;0BACP,8OAAC,oJAAO;;;;;;;;;;;AAGd", "debugId": null}}]}
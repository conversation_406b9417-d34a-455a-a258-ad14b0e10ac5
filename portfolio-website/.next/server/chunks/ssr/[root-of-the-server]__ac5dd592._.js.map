{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_a71539c9-module__T19VSG__className\",\n  \"variable\": \"geist_a71539c9-module__T19VSG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_8d43a2aa-module__8Li5zG__className\",\n  \"variable\": \"geist_mono_8d43a2aa-module__8Li5zG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/theme-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/theme-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/navigation.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,aAAa,IAAA,wQAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/navigation.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,aAAa,IAAA,wQAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/structured-data.tsx"], "sourcesContent": ["export function StructuredData() {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Person\",\n    name: \"<PERSON>\",\n    jobTitle: \"Full-Stack Software Engineer\",\n    description: \"Full-stack software engineer specializing in modern web technologies. Building scalable applications with React, Node.js, and cloud technologies.\",\n    url: \"https://johndoe.dev\",\n    email: \"<EMAIL>\",\n    telephone: \"******-123-4567\",\n    address: {\n      \"@type\": \"PostalAddress\",\n      addressLocality: \"San Francisco\",\n      addressRegion: \"CA\",\n      addressCountry: \"US\",\n    },\n    sameAs: [\n      \"https://github.com/johndoe\",\n      \"https://linkedin.com/in/johndoe\",\n      \"https://twitter.com/johndoe\",\n    ],\n    knowsAbout: [\n      \"JavaScript\",\n      \"TypeScript\",\n      \"React\",\n      \"Next.js\",\n      \"Node.js\",\n      \"Python\",\n      \"PostgreSQL\",\n      \"MongoDB\",\n      \"AWS\",\n      \"Docker\",\n      \"Full-Stack Development\",\n      \"Web Development\",\n      \"Mobile Development\",\n      \"DevOps\",\n      \"Cloud Computing\",\n    ],\n    alumniOf: {\n      \"@type\": \"EducationalOrganization\",\n      name: \"University of Technology\",\n    },\n    worksFor: {\n      \"@type\": \"Organization\",\n      name: \"Freelance\",\n    },\n  };\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAO,SAAS;IACd,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,UAAU;QACV,aAAa;QACb,KAAK;QACL,OAAO;QACP,WAAW;QACX,SAAS;YACP,SAAS;YACT,iBAAiB;YACjB,eAAe;YACf,gBAAgB;QAClB;QACA,QAAQ;YACN;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR,SAAS;YACT,MAAM;QACR;QACA,UAAU;YACR,SAAS;YACT,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON> } from \"next/font/google\";\nimport { ThemeProvider } from \"@/components/theme-provider\";\nimport { Navigation } from \"@/components/navigation\";\nimport { StructuredData } from \"@/components/structured-data\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"<PERSON> - Software Engineer\",\n  description:\n    \"Full-stack software engineer specializing in modern web technologies. Building scalable applications with React, Node.js, and cloud technologies.\",\n  keywords: [\"software engineer\", \"full-stack developer\", \"React\", \"Node.js\", \"TypeScript\", \"web development\"],\n  authors: [{ name: \"<PERSON>\" }],\n  creator: \"<PERSON>\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://johndoe.dev\",\n    title: \"<PERSON> Engineer\",\n    description: \"Full-stack software engineer specializing in modern web technologies.\",\n    siteName: \"<PERSON>\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"<PERSON> Doe - Software Engineer\",\n    description: \"Full-stack software engineer specializing in modern web technologies.\",\n    creator: \"@johndoe\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <head>\n        <StructuredData />\n      </head>\n      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>\n        <ThemeProvider attribute=\"class\" defaultTheme=\"system\" enableSystem disableTransitionOnChange>\n          <div className=\"relative flex min-h-screen flex-col\">\n            <Navigation />\n            <main className=\"flex-1\">{children}</main>\n          </div>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;;;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aACE;IACF,UAAU;QAAC;QAAqB;QAAwB;QAAS;QAAW;QAAc;KAAkB;IAC5G,SAAS;QAAC;YAAE,MAAM;QAAW;KAAE;IAC/B,SAAS;IACT,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;0BACC,cAAA,8OAAC,0JAAc;;;;;;;;;;0BAEjB,8OAAC;gBAAK,WAAW,GAAG,oJAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,yJAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;0BACxE,cAAA,8OAAC,wJAAa;oBAAC,WAAU;oBAAQ,cAAa;oBAAS,YAAY;oBAAC,yBAAyB;8BAC3F,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAU;;;;;0CACX,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}
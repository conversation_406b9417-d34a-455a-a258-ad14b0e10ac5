{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { ArrowDown, Download, Github, Linkedin, Mail } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    url: \"https://github.com/johndoe\",\n    icon: Github,\n  },\n  {\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/johndoe\",\n    icon: Linkedin,\n  },\n  {\n    name: \"Email\",\n    url: \"mailto:<EMAIL>\",\n    icon: Mail,\n  },\n];\n\nexport function Hero() {\n  const scrollToAbout = () => {\n    const aboutSection = document.getElementById(\"about\");\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-secondary/10\" />\n\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.3, 0.5, 0.3],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            opacity: [0.5, 0.3, 0.5],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 4,\n          }}\n        />\n      </div>\n\n      <div className=\"container relative z-10\">\n        <div className=\"flex flex-col lg:flex-row items-center justify-between gap-12\">\n          {/* Content */}\n          <motion.div\n            className=\"flex-1 text-center lg:text-left\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n          >\n            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className=\"mb-4\">\n              <span className=\"text-sm font-medium text-primary bg-primary/10 px-3 py-1 rounded-full\">👋 Hello, I'm</span>\n            </motion.div>\n\n            <motion.h1\n              className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n            >\n              <span className=\"bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent\">John Doe</span>\n            </motion.h1>\n\n            <motion.h2\n              className=\"text-xl md:text-2xl lg:text-3xl text-muted-foreground mb-6\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n            >\n              Full-Stack Software Engineer\n            </motion.h2>\n\n            <motion.p\n              className=\"text-lg text-muted-foreground mb-8 max-w-2xl\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n            >\n              I craft exceptional digital experiences with modern technologies. Passionate about building scalable applications that solve real-world problems\n              and deliver outstanding user experiences.\n            </motion.p>\n\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4 mb-8\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 1 }}\n            >\n              <Button size=\"lg\" onClick={scrollToAbout}>\n                View My Work\n                <ArrowDown className=\"ml-2 h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                <Download className=\"mr-2 h-4 w-4\" />\n                Download Resume\n              </Button>\n            </motion.div>\n\n            <motion.div\n              className=\"flex justify-center lg:justify-start gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 1.2 }}\n            >\n              {socialLinks.map((link, index) => (\n                <motion.a\n                  key={link.name}\n                  href={link.url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-3 rounded-full bg-secondary hover:bg-secondary/80 transition-colors\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.3, delay: 1.4 + index * 0.1 }}\n                >\n                  <link.icon className=\"h-5 w-5\" />\n                  <span className=\"sr-only\">{link.name}</span>\n                </motion.a>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Profile Image */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <div className=\"relative\">\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-full blur-2xl opacity-20\"\n                animate={{\n                  scale: [1, 1.1, 1],\n                  opacity: [0.2, 0.3, 0.2],\n                }}\n                transition={{\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                }}\n              />\n              <div className=\"relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-primary/20\">\n                <Image src=\"/placeholder.svg\" alt=\"John Doe - Professional headshot\" fill className=\"object-cover\" priority />\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll indicator */}\n      <motion.div\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 1.5 }}\n      >\n        <motion.button\n          onClick={scrollToAbout}\n          className=\"p-2 rounded-full bg-background/80 backdrop-blur-sm border border-border hover:bg-background transition-colors\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n        >\n          <ArrowDown className=\"h-4 w-4\" />\n          <span className=\"sr-only\">Scroll to about section</span>\n        </motion.button>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAQA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,KAAK;QACL,MAAM,gNAAM;IACd;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM,sNAAQ;IAChB;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM,0MAAI;IACZ;CACD;AAEM,SAAS;IACd,MAAM,gBAAgB;QACpB,MAAM,eAAe,SAAS,cAAc,CAAC;QAC7C,IAAI,cAAc;YAChB,aAAa,cAAc,CAAC;gBAAE,UAAU;YAAS;QACnD;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAG;6BAAI;4BACpB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;;8CAE7C,8OAAC,oMAAM,CAAC,GAAG;oCAAC,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAAG,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAAG,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCAAG,WAAU;8CAC9H,cAAA,8OAAC;wCAAK,WAAU;kDAAwE;;;;;;;;;;;8CAG1F,8OAAC,oMAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC;wCAAK,WAAU;kDAA4E;;;;;;;;;;;8CAG9F,8OAAC,oMAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAID,8OAAC,oMAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAKD,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAE;;sDAEtC,8OAAC,4IAAM;4CAAC,MAAK;4CAAK,SAAS;;gDAAe;8DAExC,8OAAC,6NAAS;oDAAC,WAAU;;;;;;;;;;;;sDAEvB,8OAAC,4IAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,sNAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKzC,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAEvC,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,oMAAM,CAAC,CAAC;4CAEP,MAAM,KAAK,GAAG;4CACd,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;;8DAEtD,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAW,KAAK,IAAI;;;;;;;2CAZ/B,KAAK,IAAI;;;;;;;;;;;;;;;;sCAmBtB,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;wCAC1B;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;;;;;;kDAEF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAK;4CAAC,KAAI;4CAAmB,KAAI;4CAAmC,IAAI;4CAAC,WAAU;4CAAe,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrH,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,oMAAM,CAAC,MAAM;oBACZ,SAAS;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAY;;sCAE/D,8OAAC,6NAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAKpC", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/about.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, MapPin, Award, Users } from \"lucide-react\";\n\nconst stats = [\n  {\n    icon: Calendar,\n    label: \"Years Experience\",\n    value: \"5+\",\n  },\n  {\n    icon: Award,\n    label: \"Projects Completed\",\n    value: \"50+\",\n  },\n  {\n    icon: Users,\n    label: \"Happy Clients\",\n    value: \"30+\",\n  },\n  {\n    icon: MapPin,\n    label: \"Countries Worked\",\n    value: \"10+\",\n  },\n];\n\nconst skills = [\n  { name: \"Frontend Development\", level: 95 },\n  { name: \"Backend Development\", level: 90 },\n  { name: \"Database Design\", level: 85 },\n  { name: \"DevOps & Cloud\", level: 80 },\n  { name: \"Mobile Development\", level: 75 },\n  { name: \"UI/UX Design\", level: 70 },\n];\n\nexport function About() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-secondary/20\">\n      <div className=\"container\">\n        <motion.div\n          ref={ref}\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">About Me</h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Passionate software engineer with a love for creating innovative solutions and building exceptional digital experiences.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div initial={{ opacity: 0, x: -50 }} animate={inView ? { opacity: 1, x: 0 } : {}} transition={{ duration: 0.8, delay: 0.2 }}>\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-2xl font-semibold mb-4\">My Journey</h3>\n                <p className=\"text-muted-foreground leading-relaxed\">\n                  I'm a passionate full-stack software engineer with over 5 years of experience building scalable web applications and mobile solutions. My\n                  journey began with a Computer Science degree, and I've since worked with startups and established companies, helping them bring their digital\n                  visions to life.\n                </p>\n              </div>\n\n              <div>\n                <p className=\"text-muted-foreground leading-relaxed\">\n                  I specialize in modern web technologies including React, Node.js, TypeScript, and cloud platforms. I'm particularly interested in creating\n                  performant, accessible, and user-friendly applications that solve real-world problems.\n                </p>\n              </div>\n\n              <div>\n                <h4 className=\"text-lg font-semibold mb-3\">What I Do</h4>\n                <ul className=\"space-y-2 text-muted-foreground\">\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                    Full-stack web application development\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                    Mobile app development with React Native\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                    Cloud architecture and DevOps\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                    UI/UX design and prototyping\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Image and Stats */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"relative\">\n              <div className=\"relative w-full max-w-md mx-auto\">\n                <div className=\"aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-primary/10 to-secondary/10\">\n                  <Image src=\"/placeholder.svg\" alt=\"John Doe working at his desk with multiple monitors\" fill className=\"object-cover\" />\n                </div>\n                <div className=\"absolute -bottom-6 -right-6 w-24 h-24 bg-primary rounded-2xl flex items-center justify-center\">\n                  <span className=\"text-2xl font-bold text-primary-foreground\">5+</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Stats Grid */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={inView ? { opacity: 1, y: 0 } : {}}\n                  transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}\n                  className=\"bg-background rounded-lg p-4 text-center border\"\n                >\n                  <stat.icon className=\"h-6 w-6 mx-auto mb-2 text-primary\" />\n                  <div className=\"text-2xl font-bold text-primary\">{stat.value}</div>\n                  <div className=\"text-sm text-muted-foreground\">{stat.label}</div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Skills Section */}\n        <motion.div initial={{ opacity: 0, y: 50 }} animate={inView ? { opacity: 1, y: 0 } : {}} transition={{ duration: 0.8, delay: 0.8 }} className=\"mt-20\">\n          <h3 className=\"text-2xl font-semibold mb-8 text-center\">Skills & Expertise</h3>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {skills.map((skill, index) => (\n              <motion.div\n                key={skill.name}\n                initial={{ opacity: 0, x: -20 }}\n                animate={inView ? { opacity: 1, x: 0 } : {}}\n                transition={{ duration: 0.6, delay: 1 + index * 0.1 }}\n                className=\"space-y-2\"\n              >\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"font-medium\">{skill.name}</span>\n                  <span className=\"text-sm text-muted-foreground\">{skill.level}%</span>\n                </div>\n                <div className=\"h-2 bg-secondary rounded-full overflow-hidden\">\n                  <motion.div\n                    className=\"h-full bg-gradient-to-r from-primary to-primary/80 rounded-full\"\n                    initial={{ width: 0 }}\n                    animate={inView ? { width: `${skill.level}%` } : {}}\n                    transition={{ duration: 1, delay: 1.2 + index * 0.1, ease: \"easeOut\" }}\n                  />\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;AAQA,MAAM,QAAQ;IACZ;QACE,MAAM,sNAAQ;QACd,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,6MAAK;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,6MAAK;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,oNAAM;QACZ,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,SAAS;IACb;QAAE,MAAM;QAAwB,OAAO;IAAG;IAC1C;QAAE,MAAM;QAAuB,OAAO;IAAG;IACzC;QAAE,MAAM;QAAmB,OAAO;IAAG;IACrC;QAAE,MAAM;QAAkB,OAAO;IAAG;IACpC;QAAE,MAAM;QAAsB,OAAO;IAAG;IACxC;QAAE,MAAM;QAAgB,OAAO;IAAG;CACnC;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,oMAAM,CAAC,GAAG;4BAAC,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAAG,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAAG,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAChI,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAOvD,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;kDAMvD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;;;;;;4DAA8C;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;;;;;;4DAA8C;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;;;;;;4DAA8C;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;;;;;;4DAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASxE,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wIAAK;oDAAC,KAAI;oDAAmB,KAAI;oDAAsD,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEzG,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;;;;;8CAMnE,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,oMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC1C,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAI,WAAU;8DAAmC,KAAK,KAAK;;;;;;8DAC5D,8OAAC;oDAAI,WAAU;8DAAiC,KAAK,KAAK;;;;;;;2CARrD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;8BAgBzB,8OAAC,oMAAM,CAAC,GAAG;oBAAC,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAAG,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAAG,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBAAG,WAAU;;sCAC5I,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,oMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO,IAAI,QAAQ;oCAAI;oCACpD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAe,MAAM,IAAI;;;;;;8DACzC,8OAAC;oDAAK,WAAU;;wDAAiC,MAAM,KAAK;wDAAC;;;;;;;;;;;;;sDAE/D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS,SAAS;oDAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;gDAAC,IAAI,CAAC;gDAClD,YAAY;oDAAE,UAAU;oDAAG,OAAO,MAAM,QAAQ;oDAAK,MAAM;gDAAU;;;;;;;;;;;;mCAfpE,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyB/B", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,IAAA,uKAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/projects.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { ExternalLink, Github, Star } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\n\nconst projects = [\n  {\n    id: \"1\",\n    title: \"E-Commerce Platform\",\n    description: \"A full-stack e-commerce solution with modern UI, payment integration, and admin dashboard.\",\n    longDescription:\n      \"Built with Next.js, TypeScript, Stripe, and PostgreSQL. Features include user authentication, product management, order processing, and analytics dashboard.\",\n    technologies: [\"Next.js\", \"TypeScript\", \"PostgreSQL\", \"Stripe\", \"Tailwind CSS\"],\n    imageUrl: \"/placeholder.svg\",\n    demoUrl: \"https://demo-ecommerce.johndoe.dev\",\n    githubUrl: \"https://github.com/johndoe/ecommerce-platform\",\n    featured: true,\n    category: \"Full-Stack\",\n  },\n  {\n    id: \"2\",\n    title: \"Task Management App\",\n    description: \"A collaborative task management application with real-time updates and team features.\",\n    longDescription: \"React-based SPA with Node.js backend, Socket.io for real-time collaboration, and MongoDB for data persistence.\",\n    technologies: [\"React\", \"Node.js\", \"Socket.io\", \"MongoDB\", \"Material-UI\"],\n    imageUrl: \"/placeholder.svg\",\n    demoUrl: \"https://taskapp.johndoe.dev\",\n    githubUrl: \"https://github.com/johndoe/task-manager\",\n    featured: true,\n    category: \"Web App\",\n  },\n  {\n    id: \"3\",\n    title: \"Weather Dashboard\",\n    description: \"A responsive weather dashboard with location-based forecasts and interactive maps.\",\n    longDescription: \"Built with React and integrated with multiple weather APIs. Features geolocation, interactive maps, and detailed weather analytics.\",\n    technologies: [\"React\", \"Weather API\", \"Mapbox\", \"Chart.js\", \"CSS3\"],\n    imageUrl: \"/placeholder.svg\",\n    demoUrl: \"https://weather.johndoe.dev\",\n    githubUrl: \"https://github.com/johndoe/weather-dashboard\",\n    featured: false,\n    category: \"Frontend\",\n  },\n  {\n    id: \"4\",\n    title: \"Mobile Fitness Tracker\",\n    description: \"Cross-platform mobile app for fitness tracking with workout plans and progress analytics.\",\n    longDescription: \"React Native app with Firebase backend, featuring workout tracking, progress visualization, and social features.\",\n    technologies: [\"React Native\", \"Firebase\", \"Redux\", \"Expo\", \"TypeScript\"],\n    imageUrl: \"/placeholder.svg\",\n    demoUrl: \"https://apps.apple.com/fitness-tracker\",\n    githubUrl: \"https://github.com/johndoe/fitness-tracker\",\n    featured: true,\n    category: \"Mobile\",\n  },\n  {\n    id: \"5\",\n    title: \"API Gateway Service\",\n    description: \"Microservices API gateway with authentication, rate limiting, and monitoring.\",\n    longDescription: \"Built with Node.js and Express, featuring JWT authentication, Redis caching, and comprehensive logging.\",\n    technologies: [\"Node.js\", \"Express\", \"Redis\", \"JWT\", \"Docker\"],\n    imageUrl: \"/placeholder.svg\",\n    githubUrl: \"https://github.com/johndoe/api-gateway\",\n    featured: false,\n    category: \"Backend\",\n  },\n  {\n    id: \"6\",\n    title: \"Portfolio Website\",\n    description: \"Modern, responsive portfolio website with dark mode and smooth animations.\",\n    longDescription: \"Built with Next.js 14, TypeScript, Tailwind CSS, and Framer Motion. Features SSG, SEO optimization, and accessibility compliance.\",\n    technologies: [\"Next.js\", \"TypeScript\", \"Tailwind CSS\", \"Framer Motion\"],\n    imageUrl: \"/placeholder.svg\",\n    demoUrl: \"https://johndoe.dev\",\n    githubUrl: \"https://github.com/johndoe/portfolio\",\n    featured: false,\n    category: \"Frontend\",\n  },\n];\n\nexport function Projects() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [filter, setFilter] = React.useState(\"All\");\n  const categories = [\"All\", \"Full-Stack\", \"Frontend\", \"Backend\", \"Mobile\", \"Web App\"];\n\n  const filteredProjects = filter === \"All\" ? projects : projects.filter((project) => project.category === filter);\n\n  const featuredProjects = projects.filter((project) => project.featured);\n\n  return (\n    <section id=\"projects\" className=\"py-20\">\n      <div className=\"container\">\n        <motion.div\n          ref={ref}\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Featured Projects</h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A showcase of my recent work, featuring full-stack applications, mobile apps, and innovative solutions.\n          </p>\n        </motion.div>\n\n        {/* Featured Projects */}\n        <div className=\"mb-16\">\n          <h3 className=\"text-2xl font-semibold mb-8\">Highlighted Work</h3>\n          <div className=\"grid lg:grid-cols-2 gap-8\">\n            {featuredProjects.slice(0, 2).map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, y: 50 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.8, delay: index * 0.2 }}\n                className=\"group relative bg-card rounded-xl overflow-hidden border hover:shadow-lg transition-all duration-300\"\n              >\n                <div className=\"aspect-video relative overflow-hidden\">\n                  <Image src={project.imageUrl} alt={project.title} fill className=\"object-cover group-hover:scale-105 transition-transform duration-300\" />\n                  <div className=\"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4\">\n                    {project.demoUrl && (\n                      <Button size=\"sm\" asChild>\n                        <a href={project.demoUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          <ExternalLink className=\"h-4 w-4 mr-2\" />\n                          Live Demo\n                        </a>\n                      </Button>\n                    )}\n                    {project.githubUrl && (\n                      <Button size=\"sm\" variant=\"outline\" asChild>\n                        <a href={project.githubUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          <Github className=\"h-4 w-4 mr-2\" />\n                          Code\n                        </a>\n                      </Button>\n                    )}\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"text-xl font-semibold\">{project.title}</h4>\n                    <Star className=\"h-5 w-5 text-yellow-500 fill-current\" />\n                  </div>\n                  <p className=\"text-muted-foreground mb-4\">{project.longDescription}</p>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {project.technologies.map((tech) => (\n                      <Badge key={tech} variant=\"secondary\">\n                        {tech}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Filter Tabs */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"flex flex-wrap justify-center gap-2 mb-12\"\n        >\n          {categories.map((category) => (\n            <Button key={category} variant={filter === category ? \"default\" : \"outline\"} onClick={() => setFilter(category)} className=\"rounded-full\">\n              {category}\n            </Button>\n          ))}\n        </motion.div>\n\n        {/* All Projects Grid */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={inView ? { opacity: 1 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\"\n        >\n          {filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 30 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}\n              className=\"group bg-card rounded-lg overflow-hidden border hover:shadow-md transition-all duration-300\"\n            >\n              <div className=\"aspect-video relative overflow-hidden\">\n                <Image src={project.imageUrl} alt={project.title} fill className=\"object-cover group-hover:scale-105 transition-transform duration-300\" />\n              </div>\n              <div className=\"p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h4 className=\"font-semibold\">{project.title}</h4>\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {project.category}\n                  </Badge>\n                </div>\n                <p className=\"text-sm text-muted-foreground mb-3\">{project.description}</p>\n                <div className=\"flex flex-wrap gap-1 mb-3\">\n                  {project.technologies.slice(0, 3).map((tech) => (\n                    <Badge key={tech} variant=\"secondary\" className=\"text-xs\">\n                      {tech}\n                    </Badge>\n                  ))}\n                  {project.technologies.length > 3 && (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      +{project.technologies.length - 3}\n                    </Badge>\n                  )}\n                </div>\n                <div className=\"flex gap-2\">\n                  {project.demoUrl && (\n                    <Button size=\"sm\" variant=\"outline\" asChild className=\"flex-1\">\n                      <a href={project.demoUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                        <ExternalLink className=\"h-3 w-3 mr-1\" />\n                        Demo\n                      </a>\n                    </Button>\n                  )}\n                  {project.githubUrl && (\n                    <Button size=\"sm\" variant=\"outline\" asChild className=\"flex-1\">\n                      <a href={project.githubUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                        <Github className=\"h-3 w-3 mr-1\" />\n                        Code\n                      </a>\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBACE;QACF,cAAc;YAAC;YAAW;YAAc;YAAc;YAAU;SAAe;QAC/E,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAS;YAAW;YAAa;YAAW;SAAc;QACzE,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAS;YAAe;YAAU;YAAY;SAAO;QACpE,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAgB;YAAY;YAAS;YAAQ;SAAa;QACzE,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAW;YAAW;YAAS;YAAO;SAAS;QAC9D,UAAU;QACV,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAW;YAAc;YAAgB;SAAgB;QACxE,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,iNAAc,CAAC;IAC3C,MAAM,aAAa;QAAC;QAAO;QAAc;QAAY;QAAW;QAAU;KAAU;IAEpF,MAAM,mBAAmB,WAAW,QAAQ,WAAW,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,QAAQ,KAAK;IAEzG,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,QAAQ;IAEtE,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC,oMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wIAAK;oDAAC,KAAK,QAAQ,QAAQ;oDAAE,KAAK,QAAQ,KAAK;oDAAE,IAAI;oDAAC,WAAU;;;;;;8DACjE,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,OAAO,kBACd,8OAAC,4IAAM;4DAAC,MAAK;4DAAK,OAAO;sEACvB,cAAA,8OAAC;gEAAE,MAAM,QAAQ,OAAO;gEAAE,QAAO;gEAAS,KAAI;;kFAC5C,8OAAC,sOAAY;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;wDAK9C,QAAQ,SAAS,kBAChB,8OAAC,4IAAM;4DAAC,MAAK;4DAAK,SAAQ;4DAAU,OAAO;sEACzC,cAAA,8OAAC;gEAAE,MAAM,QAAQ,SAAS;gEAAE,QAAO;gEAAS,KAAI;;kFAC9C,8OAAC,gNAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAO7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAyB,QAAQ,KAAK;;;;;;sEACpD,8OAAC,0MAAI;4DAAC,WAAU;;;;;;;;;;;;8DAElB,8OAAC;oDAAE,WAAU;8DAA8B,QAAQ,eAAe;;;;;;8DAClE,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC,0IAAK;4DAAY,SAAQ;sEACvB;2DADS;;;;;;;;;;;;;;;;;mCAnCb,QAAQ,EAAE;;;;;;;;;;;;;;;;8BA+CvB,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,4IAAM;4BAAgB,SAAS,WAAW,WAAW,YAAY;4BAAW,SAAS,IAAM,UAAU;4BAAW,WAAU;sCACxH;2BADU;;;;;;;;;;8BAOjB,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,SAAS;wBAAE,SAAS;oBAAE,IAAI,CAAC;oBACpC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAET,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,oMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;4BACtD,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wIAAK;wCAAC,KAAK,QAAQ,QAAQ;wCAAE,KAAK,QAAQ,KAAK;wCAAE,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAEnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAiB,QAAQ,KAAK;;;;;;8DAC5C,8OAAC,0IAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,QAAQ,QAAQ;;;;;;;;;;;;sDAGrB,8OAAC;4CAAE,WAAU;sDAAsC,QAAQ,WAAW;;;;;;sDACtE,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,0IAAK;wDAAY,SAAQ;wDAAY,WAAU;kEAC7C;uDADS;;;;;gDAIb,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC,0IAAK;oDAAC,SAAQ;oDAAY,WAAU;;wDAAU;wDAC3C,QAAQ,YAAY,CAAC,MAAM,GAAG;;;;;;;;;;;;;sDAItC,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBACd,8OAAC,4IAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAU,OAAO;oDAAC,WAAU;8DACpD,cAAA,8OAAC;wDAAE,MAAM,QAAQ,OAAO;wDAAE,QAAO;wDAAS,KAAI;;0EAC5C,8OAAC,sOAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;gDAK9C,QAAQ,SAAS,kBAChB,8OAAC,4IAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAU,OAAO;oDAAC,WAAU;8DACpD,cAAA,8OAAC;wDAAE,MAAM,QAAQ,SAAS;wDAAE,QAAO;wDAAS,KAAI;;0EAC9C,8OAAC,gNAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2BAzCxC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAsD7B", "debugId": null}}, {"offset": {"line": 1690, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/skills.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { \n  Code2, \n  Database, \n  Globe, \n  Smartphone, \n  Cloud, \n  Palette,\n  Server,\n  GitBranch,\n  Monitor,\n  Cpu\n} from \"lucide-react\";\n\nconst skillCategories = [\n  {\n    title: \"Frontend Development\",\n    icon: Monitor,\n    skills: [\n      { name: \"React\", level: 95, color: \"bg-blue-500\" },\n      { name: \"Next.js\", level: 90, color: \"bg-gray-800\" },\n      { name: \"TypeScript\", level: 90, color: \"bg-blue-600\" },\n      { name: \"Tailwind CSS\", level: 95, color: \"bg-cyan-500\" },\n      { name: \"Vue.js\", level: 80, color: \"bg-green-500\" },\n      { name: \"HTML/CSS\", level: 95, color: \"bg-orange-500\" },\n    ],\n  },\n  {\n    title: \"Backend Development\",\n    icon: Server,\n    skills: [\n      { name: \"Node.js\", level: 90, color: \"bg-green-600\" },\n      { name: \"Python\", level: 85, color: \"bg-yellow-500\" },\n      { name: \"Express.js\", level: 90, color: \"bg-gray-700\" },\n      { name: \"GraphQL\", level: 80, color: \"bg-pink-500\" },\n      { name: \"REST APIs\", level: 95, color: \"bg-blue-500\" },\n      { name: \"Microservices\", level: 75, color: \"bg-purple-500\" },\n    ],\n  },\n  {\n    title: \"Database & Storage\",\n    icon: Database,\n    skills: [\n      { name: \"PostgreSQL\", level: 90, color: \"bg-blue-700\" },\n      { name: \"MongoDB\", level: 85, color: \"bg-green-700\" },\n      { name: \"Redis\", level: 80, color: \"bg-red-500\" },\n      { name: \"MySQL\", level: 85, color: \"bg-orange-600\" },\n      { name: \"Firebase\", level: 80, color: \"bg-yellow-600\" },\n      { name: \"Supabase\", level: 85, color: \"bg-green-500\" },\n    ],\n  },\n  {\n    title: \"DevOps & Cloud\",\n    icon: Cloud,\n    skills: [\n      { name: \"AWS\", level: 80, color: \"bg-orange-500\" },\n      { name: \"Docker\", level: 85, color: \"bg-blue-600\" },\n      { name: \"Kubernetes\", level: 70, color: \"bg-blue-500\" },\n      { name: \"Vercel\", level: 90, color: \"bg-gray-800\" },\n      { name: \"GitHub Actions\", level: 85, color: \"bg-gray-700\" },\n      { name: \"Nginx\", level: 75, color: \"bg-green-600\" },\n    ],\n  },\n  {\n    title: \"Mobile Development\",\n    icon: Smartphone,\n    skills: [\n      { name: \"React Native\", level: 85, color: \"bg-blue-500\" },\n      { name: \"Expo\", level: 80, color: \"bg-gray-800\" },\n      { name: \"Flutter\", level: 70, color: \"bg-blue-400\" },\n      { name: \"iOS Development\", level: 65, color: \"bg-gray-600\" },\n      { name: \"Android Development\", level: 65, color: \"bg-green-500\" },\n    ],\n  },\n  {\n    title: \"Tools & Others\",\n    icon: Cpu,\n    skills: [\n      { name: \"Git\", level: 95, color: \"bg-orange-600\" },\n      { name: \"VS Code\", level: 95, color: \"bg-blue-600\" },\n      { name: \"Figma\", level: 80, color: \"bg-purple-500\" },\n      { name: \"Postman\", level: 90, color: \"bg-orange-500\" },\n      { name: \"Jest\", level: 85, color: \"bg-red-600\" },\n      { name: \"Webpack\", level: 75, color: \"bg-blue-500\" },\n    ],\n  },\n];\n\nconst technologies = [\n  { name: \"JavaScript\", icon: \"🟨\", category: \"Language\" },\n  { name: \"TypeScript\", icon: \"🔷\", category: \"Language\" },\n  { name: \"Python\", icon: \"🐍\", category: \"Language\" },\n  { name: \"React\", icon: \"⚛️\", category: \"Framework\" },\n  { name: \"Next.js\", icon: \"▲\", category: \"Framework\" },\n  { name: \"Node.js\", icon: \"🟢\", category: \"Runtime\" },\n  { name: \"PostgreSQL\", icon: \"🐘\", category: \"Database\" },\n  { name: \"MongoDB\", icon: \"🍃\", category: \"Database\" },\n  { name: \"AWS\", icon: \"☁️\", category: \"Cloud\" },\n  { name: \"Docker\", icon: \"🐳\", category: \"DevOps\" },\n  { name: \"Git\", icon: \"📝\", category: \"Tool\" },\n  { name: \"Figma\", icon: \"🎨\", category: \"Design\" },\n];\n\nexport function Skills() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-secondary/20\">\n      <div className=\"container\">\n        <motion.div\n          ref={ref}\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Skills & Technologies</h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A comprehensive overview of my technical skills and the technologies \n            I work with to build exceptional digital experiences.\n          </p>\n        </motion.div>\n\n        {/* Technology Icons */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"mb-16\"\n        >\n          <h3 className=\"text-xl font-semibold mb-8 text-center\">Technologies I Work With</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {technologies.map((tech, index) => (\n              <motion.div\n                key={tech.name}\n                initial={{ opacity: 0, scale: 0 }}\n                animate={inView ? { opacity: 1, scale: 1 } : {}}\n                transition={{ duration: 0.5, delay: 0.4 + index * 0.05 }}\n                className=\"group relative bg-background rounded-lg p-4 border hover:shadow-md transition-all duration-300 hover:scale-105\"\n              >\n                <div className=\"text-2xl mb-2 text-center\">{tech.icon}</div>\n                <div className=\"text-sm font-medium text-center\">{tech.name}</div>\n                <div className=\"text-xs text-muted-foreground text-center\">{tech.category}</div>\n                \n                {/* Tooltip */}\n                <div className=\"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-foreground text-background text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\">\n                  {tech.name}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Detailed Skills */}\n        <div className=\"grid lg:grid-cols-2 xl:grid-cols-3 gap-8\">\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: 0.6 + categoryIndex * 0.1 }}\n              className=\"bg-background rounded-xl p-6 border\"\n            >\n              <div className=\"flex items-center mb-6\">\n                <div className=\"p-2 bg-primary/10 rounded-lg mr-3\">\n                  <category.icon className=\"h-6 w-6 text-primary\" />\n                </div>\n                <h3 className=\"text-lg font-semibold\">{category.title}</h3>\n              </div>\n\n              <div className=\"space-y-4\">\n                {category.skills.map((skill, skillIndex) => (\n                  <motion.div\n                    key={skill.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={inView ? { opacity: 1, x: 0 } : {}}\n                    transition={{ \n                      duration: 0.6, \n                      delay: 0.8 + categoryIndex * 0.1 + skillIndex * 0.05 \n                    }}\n                    className=\"space-y-2\"\n                  >\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm font-medium\">{skill.name}</span>\n                      <span className=\"text-xs text-muted-foreground\">{skill.level}%</span>\n                    </div>\n                    <div className=\"h-2 bg-secondary rounded-full overflow-hidden\">\n                      <motion.div\n                        className={`h-full ${skill.color} rounded-full`}\n                        initial={{ width: 0 }}\n                        animate={inView ? { width: `${skill.level}%` } : {}}\n                        transition={{ \n                          duration: 1, \n                          delay: 1 + categoryIndex * 0.1 + skillIndex * 0.05,\n                          ease: \"easeOut\" \n                        }}\n                      />\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 1.2 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-8\">\n            <h3 className=\"text-xl font-semibold mb-4\">Always Learning</h3>\n            <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n              Technology evolves rapidly, and I'm committed to continuous learning. \n              I regularly explore new frameworks, tools, and best practices to stay \n              current with industry trends and deliver cutting-edge solutions.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAkBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM,mNAAO;QACb,QAAQ;YACN;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAc;YACjD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;YACnD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAc;YACxD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAe;YACnD;gBAAE,MAAM;gBAAY,OAAO;gBAAI,OAAO;YAAgB;SACvD;IACH;IACA;QACE,OAAO;QACP,MAAM,gNAAM;QACZ,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAe;YACpD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAgB;YACpD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;YACnD;gBAAE,MAAM;gBAAa,OAAO;gBAAI,OAAO;YAAc;YACrD;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAgB;SAC5D;IACH;IACA;QACE,OAAO;QACP,MAAM,sNAAQ;QACd,QAAQ;YACN;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAe;YACpD;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAa;YAChD;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAgB;YACnD;gBAAE,MAAM;gBAAY,OAAO;gBAAI,OAAO;YAAgB;YACtD;gBAAE,MAAM;gBAAY,OAAO;gBAAI,OAAO;YAAe;SACtD;IACH;IACA;QACE,OAAO;QACP,MAAM,6MAAK;QACX,QAAQ;YACN;gBAAE,MAAM;gBAAO,OAAO;gBAAI,OAAO;YAAgB;YACjD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAc;YAClD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAc;YAClD;gBAAE,MAAM;gBAAkB,OAAO;gBAAI,OAAO;YAAc;YAC1D;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAe;SACnD;IACH;IACA;QACE,OAAO;QACP,MAAM,4NAAU;QAChB,QAAQ;YACN;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAc;YACxD;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAc;YAChD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;YACnD;gBAAE,MAAM;gBAAmB,OAAO;gBAAI,OAAO;YAAc;YAC3D;gBAAE,MAAM;gBAAuB,OAAO;gBAAI,OAAO;YAAe;SACjE;IACH;IACA;QACE,OAAO;QACP,MAAM,uMAAG;QACT,QAAQ;YACN;gBAAE,MAAM;gBAAO,OAAO;gBAAI,OAAO;YAAgB;YACjD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;YACnD;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAgB;YACnD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAgB;YACrD;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAa;YAC/C;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;SACpD;IACH;CACD;AAED,MAAM,eAAe;IACnB;QAAE,MAAM;QAAc,MAAM;QAAM,UAAU;IAAW;IACvD;QAAE,MAAM;QAAc,MAAM;QAAM,UAAU;IAAW;IACvD;QAAE,MAAM;QAAU,MAAM;QAAM,UAAU;IAAW;IACnD;QAAE,MAAM;QAAS,MAAM;QAAM,UAAU;IAAY;IACnD;QAAE,MAAM;QAAW,MAAM;QAAK,UAAU;IAAY;IACpD;QAAE,MAAM;QAAW,MAAM;QAAM,UAAU;IAAU;IACnD;QAAE,MAAM;QAAc,MAAM;QAAM,UAAU;IAAW;IACvD;QAAE,MAAM;QAAW,MAAM;QAAM,UAAU;IAAW;IACpD;QAAE,MAAM;QAAO,MAAM;QAAM,UAAU;IAAQ;IAC7C;QAAE,MAAM;QAAU,MAAM;QAAM,UAAU;IAAS;IACjD;QAAE,MAAM;QAAO,MAAM;QAAM,UAAU;IAAO;IAC5C;QAAE,MAAM;QAAS,MAAM;QAAM,UAAU;IAAS;CACjD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAOjE,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,oMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,SAAS,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI,CAAC;oCAC9C,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAK;oCACvD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAA6B,KAAK,IAAI;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAmC,KAAK,IAAI;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAA6C,KAAK,QAAQ;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;mCAZP,KAAK,IAAI;;;;;;;;;;;;;;;;8BAoBtB,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,8OAAC,oMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,gBAAgB;4BAAI;4BAC9D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,SAAS,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAAyB,SAAS,KAAK;;;;;;;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC,oMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC1C,YAAY;gDACV,UAAU;gDACV,OAAO,MAAM,gBAAgB,MAAM,aAAa;4CAClD;4CACA,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAuB,MAAM,IAAI;;;;;;sEACjD,8OAAC;4DAAK,WAAU;;gEAAiC,MAAM,KAAK;gEAAC;;;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;wDACT,WAAW,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,aAAa,CAAC;wDAC/C,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS,SAAS;4DAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;wDAAC,IAAI,CAAC;wDAClD,YAAY;4DACV,UAAU;4DACV,OAAO,IAAI,gBAAgB,MAAM,aAAa;4DAC9C,MAAM;wDACR;;;;;;;;;;;;2CAtBC,MAAM,IAAI;;;;;;;;;;;2BAhBhB,SAAS,KAAK;;;;;;;;;;8BAiDzB,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE", "debugId": null}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,mNAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,IAAA,yHAAE,EACX,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = \"Textarea\";\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,mNAAgB,CAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EACX,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/senthu/portfolio-website/src/components/sections/contact.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { \n  Mail, \n  Phone, \n  MapPin, \n  Send, \n  Github, \n  Linkedin, \n  Twitter,\n  MessageSquare,\n  Clock,\n  CheckCircle\n} from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\n\nconst contactInfo = [\n  {\n    icon: Mail,\n    label: \"Email\",\n    value: \"<EMAIL>\",\n    href: \"mailto:<EMAIL>\",\n  },\n  {\n    icon: Phone,\n    label: \"Phone\",\n    value: \"+****************\",\n    href: \"tel:+15551234567\",\n  },\n  {\n    icon: MapPin,\n    label: \"Location\",\n    value: \"San Francisco, CA\",\n    href: \"https://maps.google.com/?q=San+Francisco,+CA\",\n  },\n];\n\nconst socialLinks = [\n  {\n    name: \"GitH<PERSON>\",\n    url: \"https://github.com/johndoe\",\n    icon: Gith<PERSON>,\n    color: \"hover:text-gray-900 dark:hover:text-gray-100\",\n  },\n  {\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/johndoe\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    url: \"https://twitter.com/johndoe\",\n    icon: Twitter,\n    color: \"hover:text-blue-400\",\n  },\n];\n\nexport function Contact() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [formData, setFormData] = React.useState({\n    name: \"\",\n    email: \"\",\n    subject: \"\",\n    message: \"\",\n  });\n\n  const [isSubmitting, setIsSubmitting] = React.useState(false);\n  const [isSubmitted, setIsSubmitted] = React.useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    setIsSubmitted(true);\n    setFormData({ name: \"\", email: \"\", subject: \"\", message: \"\" });\n\n    // Reset success message after 5 seconds\n    setTimeout(() => setIsSubmitted(false), 5000);\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20\">\n      <div className=\"container\">\n        <motion.div\n          ref={ref}\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Get In Touch</h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Have a project in mind or want to collaborate? I'd love to hear from you. \n            Let's create something amazing together.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Let's Connect</h3>\n              <p className=\"text-muted-foreground mb-8\">\n                I'm always open to discussing new opportunities, interesting projects, \n                or just having a chat about technology and innovation.\n              </p>\n            </div>\n\n            {/* Contact Details */}\n            <div className=\"space-y-4\">\n              {contactInfo.map((info, index) => (\n                <motion.a\n                  key={info.label}\n                  href={info.href}\n                  target={info.href.startsWith('http') ? '_blank' : undefined}\n                  rel={info.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={inView ? { opacity: 1, x: 0 } : {}}\n                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}\n                  className=\"flex items-center p-4 bg-secondary/50 rounded-lg hover:bg-secondary/70 transition-colors group\"\n                >\n                  <div className=\"p-2 bg-primary/10 rounded-lg mr-4 group-hover:bg-primary/20 transition-colors\">\n                    <info.icon className=\"h-5 w-5 text-primary\" />\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">{info.label}</div>\n                    <div className=\"text-sm text-muted-foreground\">{info.value}</div>\n                  </div>\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6, delay: 0.8 }}\n            >\n              <h4 className=\"font-semibold mb-4\">Follow Me</h4>\n              <div className=\"flex gap-4\">\n                {socialLinks.map((link, index) => (\n                  <motion.a\n                    key={link.name}\n                    href={link.url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={inView ? { opacity: 1, scale: 1 } : {}}\n                    transition={{ duration: 0.3, delay: 1 + index * 0.1 }}\n                    className={`p-3 bg-secondary rounded-lg transition-all duration-300 ${link.color} hover:scale-110`}\n                  >\n                    <link.icon className=\"h-5 w-5\" />\n                    <span className=\"sr-only\">{link.name}</span>\n                  </motion.a>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Availability */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6, delay: 1.2 }}\n              className=\"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6\"\n            >\n              <div className=\"flex items-center mb-2\">\n                <Clock className=\"h-5 w-5 text-primary mr-2\" />\n                <span className=\"font-semibold\">Availability</span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                Currently available for freelance projects and full-time opportunities. \n                I typically respond within 24 hours.\n              </p>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <div className=\"bg-card rounded-xl p-8 border\">\n              <div className=\"flex items-center mb-6\">\n                <MessageSquare className=\"h-6 w-6 text-primary mr-3\" />\n                <h3 className=\"text-xl font-semibold\">Send a Message</h3>\n              </div>\n\n              {isSubmitted && (\n                <motion.div\n                  initial={{ opacity: 0, y: -10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\"\n                >\n                  <div className=\"flex items-center text-green-800 dark:text-green-200\">\n                    <CheckCircle className=\"h-5 w-5 mr-2\" />\n                    <span className=\"font-medium\">Message sent successfully!</span>\n                  </div>\n                  <p className=\"text-sm text-green-600 dark:text-green-300 mt-1\">\n                    Thank you for reaching out. I'll get back to you soon.\n                  </p>\n                </motion.div>\n              )}\n\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium mb-2\">\n                      Name *\n                    </label>\n                    <Input\n                      id=\"name\"\n                      name=\"name\"\n                      type=\"text\"\n                      required\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      placeholder=\"Your full name\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium mb-2\">\n                      Email *\n                    </label>\n                    <Input\n                      id=\"email\"\n                      name=\"email\"\n                      type=\"email\"\n                      required\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label htmlFor=\"subject\" className=\"block text-sm font-medium mb-2\">\n                    Subject *\n                  </label>\n                  <Input\n                    id=\"subject\"\n                    name=\"subject\"\n                    type=\"text\"\n                    required\n                    value={formData.subject}\n                    onChange={handleInputChange}\n                    placeholder=\"What's this about?\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"message\" className=\"block text-sm font-medium mb-2\">\n                    Message *\n                  </label>\n                  <Textarea\n                    id=\"message\"\n                    name=\"message\"\n                    required\n                    rows={5}\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    placeholder=\"Tell me about your project or idea...\"\n                  />\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full\"\n                  size=\"lg\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                      Sending...\n                    </>\n                  ) : (\n                    <>\n                      <Send className=\"h-4 w-4 mr-2\" />\n                      Send Message\n                    </>\n                  )}\n                </Button>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AAnBA;;;;;;;;;AAqBA,MAAM,cAAc;IAClB;QACE,MAAM,0MAAI;QACV,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,6MAAK;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,oNAAM;QACZ,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,KAAK;QACL,MAAM,gNAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM,sNAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM,mNAAO;QACb,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,iNAAc,CAAC;QAC7C,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,iNAAc,CAAC;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,iNAAc,CAAC;IAErD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,eAAe;QACf,YAAY;YAAE,MAAM;YAAI,OAAO;YAAI,SAAS;YAAI,SAAS;QAAG;QAE5D,wCAAwC;QACxC,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,oMAAM,CAAC,CAAC;4CAEP,MAAM,KAAK,IAAI;4CACf,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;4CAClD,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;4CAC5D,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC1C,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAe,KAAK,KAAK;;;;;;sEACxC,8OAAC;4DAAI,WAAU;sEAAiC,KAAK,KAAK;;;;;;;;;;;;;2CAdvD,KAAK,KAAK;;;;;;;;;;8CAqBrB,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,oMAAM,CAAC,CAAC;oDAEP,MAAM,KAAK,GAAG;oDACd,QAAO;oDACP,KAAI;oDACJ,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,SAAS,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE,IAAI,CAAC;oDAC9C,YAAY;wDAAE,UAAU;wDAAK,OAAO,IAAI,QAAQ;oDAAI;oDACpD,WAAW,CAAC,wDAAwD,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC;;sEAElG,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAK,WAAU;sEAAW,KAAK,IAAI;;;;;;;mDAV/B,KAAK,IAAI;;;;;;;;;;;;;;;;8CAiBtB,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6MAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAQjD,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,yOAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;;;;;;;oCAGvC,6BACC,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0OAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;0DAEhC,8OAAC;gDAAE,WAAU;0DAAkD;;;;;;;;;;;;kDAMnE,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAAiC;;;;;;0EAGjE,8OAAC,0IAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,QAAQ;gEACR,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAiC;;;;;;0EAGlE,8OAAC,0IAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,QAAQ;gEACR,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAiC;;;;;;kEAGpE,8OAAC,0IAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAiC;;;;;;kEAGpE,8OAAC,gJAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,MAAM;wDACN,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC,4IAAM;gDACL,MAAK;gDACL,UAAU;gDACV,WAAU;gDACV,MAAK;0DAEJ,6BACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;wDAAmE;;iFAIpF;;sEACE,8OAAC,0MAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD", "debugId": null}}]}
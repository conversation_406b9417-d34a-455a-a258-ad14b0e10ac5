{"name": "postcss-load-config", "version": "4.0.2", "description": "Autoload Config for PostCSS", "main": "src/index.js", "types": "src/index.d.ts", "files": ["src"], "engines": {"node": ">= 14"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"ts-node": {"optional": true}, "postcss": {"optional": true}}, "keywords": ["postcss", "postcssrc", "postcss.config.js"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "repository": "postcss/postcss-load-config", "license": "MIT"}
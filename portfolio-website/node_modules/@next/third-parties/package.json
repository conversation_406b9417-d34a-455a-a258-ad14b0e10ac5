{"name": "@next/third-parties", "version": "15.5.4", "repository": {"url": "vercel/next.js", "directory": "packages/third-parties"}, "exports": {"./google": {"types": "./dist/google/index.d.ts", "default": "./dist/google/index.js"}}, "files": ["dist", "google.d.ts"], "license": "MIT", "scripts": {"build": "node ../../scripts/rm.mjs dist && tsc -d -p tsconfig.json", "prepublishOnly": "cd ../../ && turbo run build", "dev": "tsc -d -w -p tsconfig.json", "typescript": "tsec --noEmit -p tsconfig.json"}, "dependencies": {"third-party-capital": "1.0.20"}, "devDependencies": {"next": "15.5.4", "outdent": "0.8.0", "prettier": "2.5.1", "typescript": "5.8.2"}, "peerDependencies": {"next": "^13.0.0 || ^14.0.0 || ^15.0.0", "react": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0"}}
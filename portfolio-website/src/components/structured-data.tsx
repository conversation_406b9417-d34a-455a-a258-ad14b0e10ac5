export function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    name: "<PERSON>",
    jobTitle: "Full-Stack Software Engineer",
    description: "Full-stack software engineer specializing in modern web technologies. Building scalable applications with React, Node.js, and cloud technologies.",
    url: "https://johndoe.dev",
    email: "<EMAIL>",
    telephone: "******-123-4567",
    address: {
      "@type": "PostalAddress",
      addressLocality: "San Francisco",
      addressRegion: "CA",
      addressCountry: "US",
    },
    sameAs: [
      "https://github.com/johndoe",
      "https://linkedin.com/in/johndoe",
      "https://twitter.com/johndoe",
    ],
    knowsAbout: [
      "JavaScript",
      "TypeScript",
      "React",
      "Next.js",
      "Node.js",
      "Python",
      "PostgreSQL",
      "MongoDB",
      "AWS",
      "Docker",
      "Full-Stack Development",
      "Web Development",
      "Mobile Development",
      "DevOps",
      "Cloud Computing",
    ],
    alumniOf: {
      "@type": "EducationalOrganization",
      name: "University of Technology",
    },
    worksFor: {
      "@type": "Organization",
      name: "Freelance",
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

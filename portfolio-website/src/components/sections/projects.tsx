"use client";

import * as React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { ExternalLink, Github, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const projects = [
  {
    id: "1",
    title: "E-Commerce Platform",
    description: "A full-stack e-commerce solution with modern UI, payment integration, and admin dashboard.",
    longDescription:
      "Built with Next.js, TypeScript, Stripe, and PostgreSQL. Features include user authentication, product management, order processing, and analytics dashboard.",
    technologies: ["Next.js", "TypeScript", "PostgreSQL", "Stripe", "Tailwind CSS"],
    imageUrl: "/placeholder.svg",
    demoUrl: "https://demo-ecommerce.johndoe.dev",
    githubUrl: "https://github.com/johndoe/ecommerce-platform",
    featured: true,
    category: "Full-Stack",
  },
  {
    id: "2",
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates and team features.",
    longDescription: "React-based SPA with Node.js backend, Socket.io for real-time collaboration, and MongoDB for data persistence.",
    technologies: ["React", "Node.js", "Socket.io", "MongoDB", "Material-UI"],
    imageUrl: "/placeholder.svg",
    demoUrl: "https://taskapp.johndoe.dev",
    githubUrl: "https://github.com/johndoe/task-manager",
    featured: true,
    category: "Web App",
  },
  {
    id: "3",
    title: "Weather Dashboard",
    description: "A responsive weather dashboard with location-based forecasts and interactive maps.",
    longDescription: "Built with React and integrated with multiple weather APIs. Features geolocation, interactive maps, and detailed weather analytics.",
    technologies: ["React", "Weather API", "Mapbox", "Chart.js", "CSS3"],
    imageUrl: "/placeholder.svg",
    demoUrl: "https://weather.johndoe.dev",
    githubUrl: "https://github.com/johndoe/weather-dashboard",
    featured: false,
    category: "Frontend",
  },
  {
    id: "4",
    title: "Mobile Fitness Tracker",
    description: "Cross-platform mobile app for fitness tracking with workout plans and progress analytics.",
    longDescription: "React Native app with Firebase backend, featuring workout tracking, progress visualization, and social features.",
    technologies: ["React Native", "Firebase", "Redux", "Expo", "TypeScript"],
    imageUrl: "/placeholder.svg",
    demoUrl: "https://apps.apple.com/fitness-tracker",
    githubUrl: "https://github.com/johndoe/fitness-tracker",
    featured: true,
    category: "Mobile",
  },
  {
    id: "5",
    title: "API Gateway Service",
    description: "Microservices API gateway with authentication, rate limiting, and monitoring.",
    longDescription: "Built with Node.js and Express, featuring JWT authentication, Redis caching, and comprehensive logging.",
    technologies: ["Node.js", "Express", "Redis", "JWT", "Docker"],
    imageUrl: "/placeholder.svg",
    githubUrl: "https://github.com/johndoe/api-gateway",
    featured: false,
    category: "Backend",
  },
  {
    id: "6",
    title: "Portfolio Website",
    description: "Modern, responsive portfolio website with dark mode and smooth animations.",
    longDescription: "Built with Next.js 14, TypeScript, Tailwind CSS, and Framer Motion. Features SSG, SEO optimization, and accessibility compliance.",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion"],
    imageUrl: "/placeholder.svg",
    demoUrl: "https://johndoe.dev",
    githubUrl: "https://github.com/johndoe/portfolio",
    featured: false,
    category: "Frontend",
  },
];

export function Projects() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [filter, setFilter] = React.useState("All");
  const categories = ["All", "Full-Stack", "Frontend", "Backend", "Mobile", "Web App"];

  const filteredProjects = filter === "All" ? projects : projects.filter((project) => project.category === filter);

  const featuredProjects = projects.filter((project) => project.featured);

  return (
    <section id="projects" className="py-20">
      <div className="container">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Projects</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A showcase of my recent work, featuring full-stack applications, mobile apps, and innovative solutions.
          </p>
        </motion.div>

        {/* Featured Projects */}
        <div className="mb-16">
          <h3 className="text-2xl font-semibold mb-8">Highlighted Work</h3>
          <div className="grid lg:grid-cols-2 gap-8">
            {featuredProjects.slice(0, 2).map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="group relative bg-card rounded-xl overflow-hidden border hover:shadow-lg transition-all duration-300"
              >
                <div className="aspect-video relative overflow-hidden">
                  <Image src={project.imageUrl} alt={project.title} fill className="object-cover group-hover:scale-105 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4">
                    {project.demoUrl && (
                      <Button size="sm" asChild>
                        <a href={project.demoUrl} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Live Demo
                        </a>
                      </Button>
                    )}
                    {project.githubUrl && (
                      <Button size="sm" variant="outline" asChild>
                        <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                          <Github className="h-4 w-4 mr-2" />
                          Code
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-xl font-semibold">{project.title}</h4>
                    <Star className="h-5 w-5 text-yellow-500 fill-current" />
                  </div>
                  <p className="text-muted-foreground mb-4">{project.longDescription}</p>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech) => (
                      <Badge key={tech} variant="secondary">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Filter Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex flex-wrap justify-center gap-2 mb-12"
        >
          {categories.map((category) => (
            <Button key={category} variant={filter === category ? "default" : "outline"} onClick={() => setFilter(category)} className="rounded-full">
              {category}
            </Button>
          ))}
        </motion.div>

        {/* All Projects Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
              className="group bg-card rounded-lg overflow-hidden border hover:shadow-md transition-all duration-300"
            >
              <div className="aspect-video relative overflow-hidden">
                <Image src={project.imageUrl} alt={project.title} fill className="object-cover group-hover:scale-105 transition-transform duration-300" />
              </div>
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold">{project.title}</h4>
                  <Badge variant="outline" className="text-xs">
                    {project.category}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-3">{project.description}</p>
                <div className="flex flex-wrap gap-1 mb-3">
                  {project.technologies.slice(0, 3).map((tech) => (
                    <Badge key={tech} variant="secondary" className="text-xs">
                      {tech}
                    </Badge>
                  ))}
                  {project.technologies.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{project.technologies.length - 3}
                    </Badge>
                  )}
                </div>
                <div className="flex gap-2">
                  {project.demoUrl && (
                    <Button size="sm" variant="outline" asChild className="flex-1">
                      <a href={project.demoUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Demo
                      </a>
                    </Button>
                  )}
                  {project.githubUrl && (
                    <Button size="sm" variant="outline" asChild className="flex-1">
                      <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                        <Github className="h-3 w-3 mr-1" />
                        Code
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}

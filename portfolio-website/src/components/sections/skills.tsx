"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { 
  Code2, 
  Database, 
  Globe, 
  Smartphone, 
  Cloud, 
  Palette,
  Server,
  GitBranch,
  Monitor,
  Cpu
} from "lucide-react";

const skillCategories = [
  {
    title: "Frontend Development",
    icon: Monitor,
    skills: [
      { name: "React", level: 95, color: "bg-blue-500" },
      { name: "Next.js", level: 90, color: "bg-gray-800" },
      { name: "TypeScript", level: 90, color: "bg-blue-600" },
      { name: "Tailwind CSS", level: 95, color: "bg-cyan-500" },
      { name: "Vue.js", level: 80, color: "bg-green-500" },
      { name: "HTML/CSS", level: 95, color: "bg-orange-500" },
    ],
  },
  {
    title: "Backend Development",
    icon: Server,
    skills: [
      { name: "Node.js", level: 90, color: "bg-green-600" },
      { name: "Python", level: 85, color: "bg-yellow-500" },
      { name: "Express.js", level: 90, color: "bg-gray-700" },
      { name: "GraphQL", level: 80, color: "bg-pink-500" },
      { name: "REST APIs", level: 95, color: "bg-blue-500" },
      { name: "Microservices", level: 75, color: "bg-purple-500" },
    ],
  },
  {
    title: "Database & Storage",
    icon: Database,
    skills: [
      { name: "PostgreSQL", level: 90, color: "bg-blue-700" },
      { name: "MongoDB", level: 85, color: "bg-green-700" },
      { name: "Redis", level: 80, color: "bg-red-500" },
      { name: "MySQL", level: 85, color: "bg-orange-600" },
      { name: "Firebase", level: 80, color: "bg-yellow-600" },
      { name: "Supabase", level: 85, color: "bg-green-500" },
    ],
  },
  {
    title: "DevOps & Cloud",
    icon: Cloud,
    skills: [
      { name: "AWS", level: 80, color: "bg-orange-500" },
      { name: "Docker", level: 85, color: "bg-blue-600" },
      { name: "Kubernetes", level: 70, color: "bg-blue-500" },
      { name: "Vercel", level: 90, color: "bg-gray-800" },
      { name: "GitHub Actions", level: 85, color: "bg-gray-700" },
      { name: "Nginx", level: 75, color: "bg-green-600" },
    ],
  },
  {
    title: "Mobile Development",
    icon: Smartphone,
    skills: [
      { name: "React Native", level: 85, color: "bg-blue-500" },
      { name: "Expo", level: 80, color: "bg-gray-800" },
      { name: "Flutter", level: 70, color: "bg-blue-400" },
      { name: "iOS Development", level: 65, color: "bg-gray-600" },
      { name: "Android Development", level: 65, color: "bg-green-500" },
    ],
  },
  {
    title: "Tools & Others",
    icon: Cpu,
    skills: [
      { name: "Git", level: 95, color: "bg-orange-600" },
      { name: "VS Code", level: 95, color: "bg-blue-600" },
      { name: "Figma", level: 80, color: "bg-purple-500" },
      { name: "Postman", level: 90, color: "bg-orange-500" },
      { name: "Jest", level: 85, color: "bg-red-600" },
      { name: "Webpack", level: 75, color: "bg-blue-500" },
    ],
  },
];

const technologies = [
  { name: "JavaScript", icon: "🟨", category: "Language" },
  { name: "TypeScript", icon: "🔷", category: "Language" },
  { name: "Python", icon: "🐍", category: "Language" },
  { name: "React", icon: "⚛️", category: "Framework" },
  { name: "Next.js", icon: "▲", category: "Framework" },
  { name: "Node.js", icon: "🟢", category: "Runtime" },
  { name: "PostgreSQL", icon: "🐘", category: "Database" },
  { name: "MongoDB", icon: "🍃", category: "Database" },
  { name: "AWS", icon: "☁️", category: "Cloud" },
  { name: "Docker", icon: "🐳", category: "DevOps" },
  { name: "Git", icon: "📝", category: "Tool" },
  { name: "Figma", icon: "🎨", category: "Design" },
];

export function Skills() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section id="skills" className="py-20 bg-secondary/20">
      <div className="container">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Skills & Technologies</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A comprehensive overview of my technical skills and the technologies 
            I work with to build exceptional digital experiences.
          </p>
        </motion.div>

        {/* Technology Icons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-16"
        >
          <h3 className="text-xl font-semibold mb-8 text-center">Technologies I Work With</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {technologies.map((tech, index) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, scale: 0 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.05 }}
                className="group relative bg-background rounded-lg p-4 border hover:shadow-md transition-all duration-300 hover:scale-105"
              >
                <div className="text-2xl mb-2 text-center">{tech.icon}</div>
                <div className="text-sm font-medium text-center">{tech.name}</div>
                <div className="text-xs text-muted-foreground text-center">{tech.category}</div>
                
                {/* Tooltip */}
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-foreground text-background text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  {tech.name}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Detailed Skills */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.6 + categoryIndex * 0.1 }}
              className="bg-background rounded-xl p-6 border"
            >
              <div className="flex items-center mb-6">
                <div className="p-2 bg-primary/10 rounded-lg mr-3">
                  <category.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">{category.title}</h3>
              </div>

              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ 
                      duration: 0.6, 
                      delay: 0.8 + categoryIndex * 0.1 + skillIndex * 0.05 
                    }}
                    className="space-y-2"
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{skill.name}</span>
                      <span className="text-xs text-muted-foreground">{skill.level}%</span>
                    </div>
                    <div className="h-2 bg-secondary rounded-full overflow-hidden">
                      <motion.div
                        className={`h-full ${skill.color} rounded-full`}
                        initial={{ width: 0 }}
                        animate={inView ? { width: `${skill.level}%` } : {}}
                        transition={{ 
                          duration: 1, 
                          delay: 1 + categoryIndex * 0.1 + skillIndex * 0.05,
                          ease: "easeOut" 
                        }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-8">
            <h3 className="text-xl font-semibold mb-4">Always Learning</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Technology evolves rapidly, and I'm committed to continuous learning. 
              I regularly explore new frameworks, tools, and best practices to stay 
              current with industry trends and deliver cutting-edge solutions.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
